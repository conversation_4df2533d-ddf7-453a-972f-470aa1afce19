#!/usr/bin/env python3
"""
使用官方文档示例测试ed25519签名
"""

import os
import base64
from cryptography.hazmat.primitives.serialization import load_pem_private_key

# 设置代理
os.environ['http_proxy'] = "http://127.0.0.1:7890"
os.environ['https_proxy'] = "http://127.0.0.1:7890"

def test_official_example():
    """测试官方文档中的示例"""
    
    # 官方文档示例参数
    params = {
        'symbol': 'BTCUSDT',
        'side': 'SELL',
        'type': 'LIMIT',
        'timeInForce': 'GTC',
        'quantity': '1.0000000',
        'price': '0.20',
        'timestamp': 1668481559918
    }
    
    print("🧪 测试官方文档示例")
    print("=" * 50)
    print(f"📋 参数: {params}")
    
    # 按照官方文档创建载荷
    payload = '&'.join([f'{param}={value}' for param, value in sorted(params.items())])
    print(f"🔗 载荷: {payload}")
    
    # 使用我们的私钥签名
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
    
    try:
        # 加载私钥
        private_key = load_pem_private_key(
            private_key_pem.encode('utf-8'),
            password=None
        )
        
        # 签名
        signature = private_key.sign(payload.encode('ASCII'))
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        
        print(f"✅ 签名成功: {signature_b64}")
        
        return True
        
    except Exception as e:
        print(f"❌ 签名失败: {e}")
        return False

def test_our_client():
    """测试我们的客户端实现"""
    
    print("\n🔧 测试我们的客户端实现")
    print("=" * 50)
    
    from binance_ed25519_client import create_ed25519_client
    
    api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
    
    try:
        client = create_ed25519_client(api_key, private_key_pem, testnet=False)
        print("✅ 客户端创建成功")
        
        # 测试签名方法
        test_payload = "price=0.20&quantity=1.0000000&side=SELL&symbol=BTCUSDT&timeInForce=GTC&timestamp=1668481559918&type=LIMIT"
        signature = client._sign_request(test_payload)
        print(f"✅ 客户端签名: {signature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_order():
    """测试简单订单"""
    
    print("\n📦 测试简单订单格式")
    print("=" * 50)
    
    from binance_ed25519_client import create_ed25519_client
    
    api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
    
    try:
        client = create_ed25519_client(api_key, private_key_pem, testnet=False)
        
        # 测试获取账户信息（需要签名）
        print("🔍 测试获取账户信息...")
        try:
            account = client.get_account()
            print("✅ 账户信息获取成功")
            
            # 显示USDT余额
            for balance in account['balances']:
                if balance['asset'] == 'USDT' and (float(balance['free']) > 0 or float(balance['locked']) > 0):
                    print(f"💰 USDT余额: {balance['free']} (可用) + {balance['locked']} (锁定)")
                    break
            
            return True
            
        except Exception as e:
            print(f"❌ 账户信息获取失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔐 Ed25519签名验证测试")
    print("=" * 60)
    
    success1 = test_official_example()
    success2 = test_our_client()
    success3 = test_simple_order()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("✅ 所有测试通过！签名实现正确")
    else:
        print("❌ 部分测试失败")
        if not success1:
            print("  - 官方示例测试失败")
        if not success2:
            print("  - 客户端实现测试失败")
        if not success3:
            print("  - API调用测试失败")

if __name__ == '__main__':
    main()
