#!/usr/bin/env python3
"""
测试ed25519客户端
"""

from binance_ed25519_client import create_ed25519_client

# 测试密钥
api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""

try:
    print("正在创建ed25519客户端...")
    client = create_ed25519_client(api_key, private_key_pem, testnet=False)
    print("✓ ed25519客户端创建成功")
    
    print("正在测试签名功能...")
    test_query = "symbol=BTCUSDT&timestamp=1234567890"
    signature = client._sign_request(test_query)
    print(f"✓ 签名成功: {signature[:20]}...")
    
    print("正在测试API调用...")
    try:
        # 测试获取价格信息（不需要签名）
        tickers = client.get_all_tickers()
        print(f"✓ 获取价格信息成功，共 {len(tickers)} 个交易对")
        
        # 找到BTCUSDT的价格
        btc_price = None
        for ticker in tickers:
            if ticker['symbol'] == 'BTCUSDT':
                btc_price = ticker['price']
                break
        
        if btc_price:
            print(f"✓ BTCUSDT 当前价格: {btc_price}")
        
    except Exception as e:
        print(f"⚠ API调用测试失败（这可能是正常的，如果API密钥无效）: {e}")
    
    print("\n测试完成！ed25519客户端工作正常。")
    
except Exception as e:
    print(f"✗ 测试失败: {e}")
    import traceback
    traceback.print_exc()
