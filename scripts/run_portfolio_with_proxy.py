#!/usr/bin/env python3
"""
带代理设置的持仓管理脚本启动器
"""

import os
import sys
import subprocess
import argparse

def set_proxy():
    """设置代理环境变量"""
    proxy_url = "http://127.0.0.1:7890"
    os.environ['http_proxy'] = proxy_url
    os.environ['https_proxy'] = proxy_url
    print(f"✓ 代理设置完成: {proxy_url}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='带代理的Binance现货持仓管理工具')
    parser.add_argument('--api_key', required=True, help='Binance API Key')
    parser.add_argument('--api_secret', help='Binance API Secret (传统HMAC密钥)')
    parser.add_argument('--ed25519_key', help='ed25519私钥PEM格式')
    parser.add_argument('--use_ed25519', action='store_true', help='使用ed25519签名')
    parser.add_argument('--live', action='store_true', help='执行实际交易（默认为模拟运行）')
    parser.add_argument('--symbols', nargs='+', default=['BTC', 'ETH', 'BNB', 'ADA', 'DOT'],
                       help='目标币种列表（默认: BTC ETH BNB ADA DOT）')
    parser.add_argument('--amount', type=float, default=20.0,
                       help='每个币种的目标USDT价值（默认: 20.0）')

    args = parser.parse_args()

    # 设置代理
    set_proxy()

    # 构建命令行参数
    cmd = ['python3', 'balance_portfolio.py']
    cmd.extend(['--api_key', args.api_key])
    
    if args.use_ed25519:
        if not args.ed25519_key:
            print("❌ 使用ed25519签名时必须提供--ed25519_key参数")
            return 1
        cmd.extend(['--use_ed25519'])
        cmd.extend(['--ed25519_key', args.ed25519_key])
    else:
        if not args.api_secret:
            print("❌ 使用传统签名时必须提供--api_secret参数")
            return 1
        cmd.extend(['--api_secret', args.api_secret])
    
    if args.live:
        cmd.append('--live')
    
    cmd.extend(['--symbols'] + args.symbols)
    cmd.extend(['--amount', str(args.amount)])

    print(f"🚀 启动持仓管理脚本...")
    print(f"📊 目标币种: {args.symbols}")
    print(f"💰 每个币种目标金额: {args.amount} USDT")
    print(f"🔐 签名方式: {'ed25519' if args.use_ed25519 else 'HMAC'}")
    print(f"⚡ 模式: {'实际交易' if args.live else '模拟运行'}")
    print()

    # 执行命令
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ 脚本执行失败: {e}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断执行")
        return 1

if __name__ == '__main__':
    exit(main())
