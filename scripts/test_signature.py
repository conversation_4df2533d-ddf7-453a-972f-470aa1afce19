#!/usr/bin/env python3
"""
测试ed25519签名实现
"""

import os
import time
import base64
from urllib.parse import urlencode

# 设置代理
os.environ['http_proxy'] = "http://127.0.0.1:7890"
os.environ['https_proxy'] = "http://127.0.0.1:7890"

try:
    from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
    from cryptography.hazmat.primitives import serialization
    print("✓ 使用cryptography库")
except ImportError:
    try:
        from ed25519 import SigningKey
        print("✓ 使用ed25519库")
    except ImportError:
        print("❌ 需要安装ed25519库: pip install cryptography")
        exit(1)

def parse_ed25519_key(pem_content: str):
    """解析ed25519私钥"""
    try:
        # 使用cryptography库直接从PEM加载
        pem_bytes = pem_content.encode('utf-8')
        private_key = serialization.load_pem_private_key(
            pem_bytes, 
            password=None
        )
        return private_key
    except Exception as e:
        print(f"❌ 解析私钥失败: {e}")
        return None

def sign_message(private_key, message: str) -> str:
    """签名消息"""
    try:
        signature = private_key.sign(message.encode('utf-8'))
        return base64.b64encode(signature).decode('utf-8')
    except Exception as e:
        print(f"❌ 签名失败: {e}")
        return None

def test_signature():
    """测试签名"""
    
    # 测试密钥
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""

    print("🔐 测试ed25519签名...")
    
    # 解析私钥
    private_key = parse_ed25519_key(private_key_pem)
    if not private_key:
        return False
    
    print("✓ 私钥解析成功")
    
    # 测试签名
    test_message = "symbol=BTCUSDT&side=BUY&type=MARKET&quoteOrderQty=20.0&timestamp=1234567890"
    signature = sign_message(private_key, test_message)
    
    if signature:
        print(f"✓ 签名成功: {signature}")
        print(f"📝 测试消息: {test_message}")
        return True
    else:
        return False

def test_binance_format():
    """测试Binance API格式"""
    print("\n🌐 测试Binance API格式...")
    
    # 模拟订单参数
    params = {
        'symbol': 'BTCUSDT',
        'side': 'BUY',
        'type': 'MARKET',
        'quoteOrderQty': '20.0',
        'timestamp': int(time.time() * 1000)
    }
    
    # 按字母顺序排序
    sorted_params = sorted(params.items())
    query_string = '&'.join([f"{k}={v}" for k, v in sorted_params])
    
    print(f"📋 参数: {params}")
    print(f"🔗 查询字符串: {query_string}")
    
    # 解析私钥并签名
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
    
    private_key = parse_ed25519_key(private_key_pem)
    if private_key:
        signature = sign_message(private_key, query_string)
        if signature:
            print(f"✅ Binance格式签名: {signature}")
            
            # 显示完整的请求URL
            final_params = dict(sorted_params)
            final_params['signature'] = signature
            final_query = urlencode(final_params)
            print(f"🌐 完整URL: https://api.binance.com/api/v3/order?{final_query}")
            return True
    
    return False

def main():
    """主函数"""
    print("🧪 ed25519签名测试")
    print("=" * 50)
    
    success1 = test_signature()
    success2 = test_binance_format()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ 所有测试通过")
    else:
        print("❌ 测试失败")

if __name__ == '__main__':
    main()
