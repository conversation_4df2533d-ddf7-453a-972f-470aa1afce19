#!/usr/bin/env python3
"""
Binance现货持仓管理脚本
自动管理指定币种的持仓，确保每个币种的USDT价值约为20 USDT
如果持仓不足，会自动买进
"""

import json
import time
import logging
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List
try:
    from binance.client import Client
    from binance.exceptions import BinanceAPIException
    BINANCE_PYTHON_AVAILABLE = True
except ImportError:
    BINANCE_PYTHON_AVAILABLE = False

from binance_ed25519_client import create_ed25519_client, BinanceAPIException

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PortfolioManager:
    def __init__(self, ak: str, sk: str, target_symbols: List[str] = None, target_amount: float = 20.0, use_ed25519: bool = False):
        """初始化持仓管理器"""
        self.use_ed25519 = use_ed25519

        if use_ed25519:
            # 使用ed25519签名
            self.client = create_ed25519_client(ak, sk, testnet=False)
        else:
            # 使用传统HMAC签名
            if not BINANCE_PYTHON_AVAILABLE:
                raise ImportError("需要安装python-binance库: pip install python-binance")
            self.client = Client(
                api_key=ak,
                api_secret=sk,
                testnet=False
            )

        # 目标币种列表（不包含USDT）
        self.target_symbols = target_symbols or ['BTC', 'ETH', 'BNB', 'ADA', 'DOT']
        # 每个币种的目标USDT价值
        self.target_amount = Decimal(str(target_amount))
        # 最小交易金额（USDT）
        self.min_trade_amount = Decimal('1.0')
        # 价格容差（避免频繁小额交易）
        self.tolerance = Decimal('0.10')  # 10%

    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.error(f"配置文件 {config_path} 不存在")
            raise
        except json.JSONDecodeError:
            logger.error(f"配置文件 {config_path} 格式错误")
            raise

    def get_account_balances(self) -> Dict[str, Decimal]:
        """获取账户余额（只返回非零余额）"""
        try:
            account_info = self.client.get_account()
            balances = {}

            for balance in account_info['balances']:
                asset = balance['asset']
                free = Decimal(balance['free'])
                locked = Decimal(balance['locked'])
                total = free + locked

                if total > 0:
                    balances[asset] = total
                    logger.info(f"{asset}: {total}")

            return balances
        except BinanceAPIException as e:
            logger.error(f"获取账户余额失败: {e}")
            raise

    def get_prices(self, symbols: List[str]) -> Dict[str, Decimal]:
        """获取价格信息"""
        try:
            prices = {}
            ticker_prices = self.client.get_all_tickers()

            for ticker in ticker_prices:
                symbol = ticker['symbol']
                if symbol in symbols:
                    prices[symbol] = Decimal(ticker['price'])

            return prices
        except BinanceAPIException as e:
            logger.error(f"获取价格信息失败: {e}")
            raise

    def calculate_usdt_values(self, balances: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """计算目标币种的USDT价值"""
        usdt_values = {}

        # 只计算目标币种的价值
        symbols_needed = []
        for asset in self.target_symbols:
            symbols_needed.append(f"{asset}USDT")

        if symbols_needed:
            prices = self.get_prices(symbols_needed)

            for asset in self.target_symbols:
                symbol = f"{asset}USDT"
                if symbol in prices:
                    balance = balances.get(asset, Decimal('0'))
                    usdt_values[asset] = balance * prices[symbol]
                    logger.info(f"{asset}: {balance} * {prices[symbol]} = {usdt_values[asset]} USDT")
                else:
                    logger.warning(f"无法获取 {symbol} 的价格")
                    usdt_values[asset] = Decimal('0')

        return usdt_values

    def calculate_target_allocation(self, usdt_values: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """计算目标分配"""
        total_current_value = sum(usdt_values.values())
        num_assets = len(self.target_symbols)
        total_target_value = self.target_amount * num_assets

        logger.info(f"当前总价值: {total_current_value} USDT")
        logger.info(f"目标币种数量: {num_assets}")
        logger.info(f"每个币种目标价值: {self.target_amount} USDT")
        logger.info(f"总目标价值: {total_target_value} USDT")

        target_allocation = {}
        for asset in self.target_symbols:
            target_allocation[asset] = self.target_amount

        return target_allocation

    def calculate_trades(self, balances: Dict[str, Decimal], usdt_values: Dict[str, Decimal],
                        target_allocation: Dict[str, Decimal]) -> List[Dict]:
        """计算需要执行的交易"""
        trades = []

        # 获取当前价格
        symbols_needed = []
        for asset in self.target_symbols:
            symbols_needed.append(f"{asset}USDT")

        prices = self.get_prices(symbols_needed) if symbols_needed else {}

        # 检查USDT余额
        usdt_balance = balances.get('USDT', Decimal('0'))
        logger.info(f"当前USDT余额: {usdt_balance}")

        for asset in self.target_symbols:
            current_value = usdt_values.get(asset, Decimal('0'))
            target_value = target_allocation[asset]
            difference = target_value - current_value

            logger.info(f"{asset}: 当前价值 {current_value} USDT, 目标价值 {target_value} USDT, 差额 {difference} USDT")

            # 检查是否需要交易（超过容差且超过最小交易金额）
            if difference > self.min_trade_amount and abs(difference) > target_value * self.tolerance:
                symbol = f"{asset}USDT"
                if symbol in prices:
                    # 需要买入
                    if difference > 0:
                        # 检查USDT余额是否足够
                        if usdt_balance >= difference:
                            trades.append({
                                'action': 'buy',
                                'symbol': symbol,
                                'usdt_amount': difference,
                                'asset': asset,
                                'price': prices[symbol]
                            })
                            usdt_balance -= difference  # 更新可用USDT余额
                        else:
                            logger.warning(f"USDT余额不足，无法买入 {asset}。需要 {difference} USDT，但只有 {usdt_balance} USDT")
                else:
                    logger.warning(f"无法获取 {symbol} 的价格，跳过交易")

        return trades

    def execute_trade(self, trade: Dict) -> bool:
        """执行单个交易"""
        try:
            if trade['action'] == 'buy':
                # 买入
                symbol = trade['symbol']
                usdt_amount = trade['usdt_amount']

                logger.info(f"买入 {symbol}: 使用 {usdt_amount} USDT")

                # 检查客户端类型并调用相应的方法
                if self.use_ed25519:
                    # 使用ed25519客户端，支持quoteOrderQty
                    order = self.client.order_market_buy(
                        symbol=symbol,
                        quoteOrderQty=str(usdt_amount)
                    )
                else:
                    # 使用传统客户端，需要计算数量
                    # 获取交易对信息
                    symbol_info = self.client.get_symbol_info(symbol)

                    # 计算数量
                    quantity = usdt_amount / trade['price']

                    # 应用数量精度
                    for filter_item in symbol_info['filters']:
                        if filter_item['filterType'] == 'LOT_SIZE':
                            step_size = Decimal(filter_item['stepSize'])
                            quantity = quantity.quantize(step_size, rounding=ROUND_DOWN)
                            break

                    order = self.client.order_market_buy(
                        symbol=symbol,
                        quantity=str(quantity)
                    )

                logger.info(f"买单成功: {order['orderId']}")
                return True

            elif trade['action'] == 'sell':
                # 卖出
                symbol = trade['symbol']
                usdt_amount = trade['usdt_amount']

                # 获取交易对信息
                symbol_info = self.client.get_symbol_info(symbol)

                # 计算数量
                quantity = usdt_amount / trade['price']

                # 应用数量精度
                for filter_item in symbol_info['filters']:
                    if filter_item['filterType'] == 'LOT_SIZE':
                        step_size = Decimal(filter_item['stepSize'])
                        quantity = quantity.quantize(step_size, rounding=ROUND_DOWN)
                        break

                logger.info(f"卖出 {symbol}: 数量 {quantity}, 预估价值 {usdt_amount} USDT")

                # 执行市价卖单
                order = self.client.order_market_sell(
                    symbol=symbol,
                    quantity=str(quantity)
                )

                logger.info(f"卖单成功: {order['orderId']}")
                return True

        except BinanceAPIException as e:
            logger.error(f"交易执行失败: {e}")
            return False
        except Exception as e:
            logger.error(f"交易执行出错: {e}")
            return False

    def manage_portfolio(self, dry_run: bool = True):
        """执行投资组合管理"""
        logger.info("开始投资组合管理...")
        logger.info(f"目标币种: {self.target_symbols}")
        logger.info(f"每个币种目标价值: {self.target_amount} USDT")

        # 1. 获取当前余额
        logger.info("获取账户余额...")
        balances = self.get_account_balances()

        if not balances:
            logger.warning("没有发现任何余额")
            return

        # 2. 计算目标币种的USDT价值
        logger.info("计算目标币种的USDT价值...")
        usdt_values = self.calculate_usdt_values(balances)

        # 3. 计算目标分配
        logger.info("计算目标分配...")
        target_allocation = self.calculate_target_allocation(usdt_values)

        # 4. 计算需要的交易
        logger.info("计算需要的交易...")
        trades = self.calculate_trades(balances, usdt_values, target_allocation)

        if not trades:
            logger.info("所有目标币种持仓已达到目标，无需交易")
            return

        # 5. 显示交易计划
        logger.info("交易计划:")
        for i, trade in enumerate(trades, 1):
            logger.info(f"{i}. {trade}")

        # 6. 执行交易
        if dry_run:
            logger.info("这是模拟运行，不会执行实际交易")
        else:
            logger.info("开始执行交易...")
            for i, trade in enumerate(trades, 1):
                logger.info(f"执行交易 {i}/{len(trades)}")
                success = self.execute_trade(trade)
                if success:
                    logger.info(f"交易 {i} 成功")
                    time.sleep(1)  # 避免API限制
                else:
                    logger.error(f"交易 {i} 失败")
                    break

        logger.info("投资组合管理完成")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='Binance现货持仓管理工具')
    parser.add_argument('--api_key', default='iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50', help='配置文件路径')
    parser.add_argument('--api_secret', default='YOUR_TRADITIONAL_API_SECRET_HERE', help='Binance API Secret (传统HMAC密钥)')
    parser.add_argument('--ed25519_key', default='''''', help='ed25519私钥PEM格式 (如果使用ed25519签名)')
    parser.add_argument('--use_ed25519', action='store_true', help='使用ed25519签名而不是HMAC')
    parser.add_argument('--live', action='store_true', help='执行实际交易（默认为模拟运行）')
    parser.add_argument('--symbols', nargs='+', default=['BTC', 'ETH', 'BNB', 'ADA', 'DOT'],
                       help='目标币种列表（默认: BTC ETH BNB ADA DOT）')
    parser.add_argument('--amount', type=float, default=20.0,
                       help='每个币种的目标USDT价值（默认: 20.0）')

    args = parser.parse_args()

    try:
        # 确定使用哪种签名方式和密钥
        if args.use_ed25519:
            if not args.ed25519_key:
                logger.error("使用ed25519签名时必须提供--ed25519_key参数")
                return 1
            secret_key = args.ed25519_key
        else:
            secret_key = args.api_secret

        manager = PortfolioManager(args.api_key, secret_key, args.symbols, args.amount, args.use_ed25519)
        manager.manage_portfolio(dry_run=not args.live)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1

    return 0

if __name__ == '__main__':
    exit(main())
