#!/usr/bin/env python3
"""
AWS测试脚本
"""

import os
import subprocess
import sys

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    
    # 激活conda环境
    conda_path = os.path.expanduser("~/miniconda3/bin/activate")
    if os.path.exists(conda_path):
        print(f"✅ 找到conda: {conda_path}")
    else:
        print("❌ 未找到conda")
        return False
    
    # 检查Python版本
    python_version = sys.version
    print(f"🐍 Python版本: {python_version}")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖...")
    
    dependencies = [
        'requests',
        'cryptography'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} 已安装")
        except ImportError:
            print(f"📥 安装 {dep}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], check=True)

def test_basic_imports():
    """测试基本导入"""
    print("\n🧪 测试基本导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
        
        from cryptography.hazmat.primitives.serialization import load_pem_private_key
        print("✅ cryptography 导入成功")
        
        # 测试ed25519客户端
        from binance_ed25519_client import create_ed25519_client
        print("✅ binance_ed25519_client 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_network():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    
    try:
        import requests
        
        # 测试基本连接
        response = requests.get('https://api.binance.com/api/v3/ping', timeout=10)
        if response.status_code == 200:
            print("✅ Binance API连接正常")
            
            # 测试获取时间
            time_response = requests.get('https://api.binance.com/api/v3/time', timeout=10)
            server_time = time_response.json()['serverTime']
            print(f"✅ 服务器时间: {server_time}")
            
            return True
        else:
            print(f"❌ 连接失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")
        return False

def test_signature():
    """测试签名功能"""
    print("\n🔐 测试签名功能...")
    
    try:
        from binance_ed25519_client import create_ed25519_client
        
        api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
        private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
        
        client = create_ed25519_client(api_key, private_key_pem, testnet=False)
        print("✅ ed25519客户端创建成功")
        
        # 测试签名
        test_payload = "symbol=BTCUSDT&timestamp=1234567890"
        signature = client._sign_request(test_payload)
        print(f"✅ 签名测试成功: {signature[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 签名测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_portfolio_script():
    """测试持仓管理脚本"""
    print("\n📊 测试持仓管理脚本...")
    
    try:
        # 测试模拟运行
        print("🧪 测试模拟运行...")
        result = subprocess.run([
            sys.executable, 'balance_portfolio.py',
            '--symbols', 'BTC', 'ETH',
            '--amount', '20.0'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 脚本运行成功")
            print("📋 输出:")
            for line in result.stdout.split('\n')[-10:]:  # 显示最后10行
                if line.strip():
                    print(f"  {line}")
            return True
        else:
            print("❌ 脚本运行失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本运行超时")
        return False
    except Exception as e:
        print(f"❌ 脚本测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 AWS环境测试")
    print("=" * 50)
    
    tests = [
        ("环境设置", setup_environment),
        ("安装依赖", install_dependencies),
        ("基本导入", test_basic_imports),
        ("网络连接", test_network),
        ("签名功能", test_signature),
        ("持仓脚本", test_portfolio_script)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*60)
    print("📋 测试结果总结:")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！可以运行持仓管理脚本")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")

if __name__ == '__main__':
    main()
