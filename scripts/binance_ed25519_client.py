#!/usr/bin/env python3
"""
支持ed25519签名的Binance客户端
"""

import json
import time
import requests
from decimal import Decimal
from typing import Dict, List, Optional
from urllib.parse import urlencode
import base64
try:
    from ed25519 import SigningKey
    ED25519_LIB = 'ed25519'
except ImportError:
    try:
        from cryptography.hazmat.primitives.asymmetric.ed25519 import Ed25519PrivateKey
        from cryptography.hazmat.primitives import serialization
        ED25519_LIB = 'cryptography'
    except ImportError:
        raise ImportError("需要安装ed25519库: pip install ed25519 或 pip install cryptography")


class BinanceEd25519Client:
    def __init__(self, api_key: str, private_key_pem: str, testnet: bool = False):
        """初始化ed25519客户端"""
        self.api_key = api_key
        self.testnet = testnet

        # 解析ed25519私钥
        self.signing_key = self._parse_private_key(private_key_pem)

        # API端点
        if testnet:
            self.base_url = "https://testnet.binance.vision"
        else:
            self.base_url = "https://api.binance.com"

    def _parse_private_key(self, pem_content: str):
        """解析PEM格式的ed25519私钥"""
        try:
            if ED25519_LIB == 'ed25519':
                # 使用ed25519库
                # 移除PEM头尾和换行符
                key_data = pem_content.replace("-----BEGIN PRIVATE KEY-----", "")
                key_data = key_data.replace("-----END PRIVATE KEY-----", "")
                key_data = key_data.replace("\n", "").replace("\r", "")

                # Base64解码
                key_bytes = base64.b64decode(key_data)

                # ed25519私钥在DER格式中的偏移量
                # DER格式: 30 2e 02 01 00 30 05 06 03 2b 65 70 04 22 04 20 [32字节私钥]
                private_key_bytes = key_bytes[-32:]  # 取最后32字节

                return SigningKey(private_key_bytes)
            else:
                # 使用cryptography库 - 直接从PEM加载
                pem_bytes = pem_content.encode('utf-8')
                return Ed25519PrivateKey.from_private_bytes(
                    serialization.load_pem_private_key(
                        pem_bytes,
                        password=None
                    ).private_bytes(
                        encoding=serialization.Encoding.Raw,
                        format=serialization.PrivateFormat.Raw,
                        encryption_algorithm=serialization.NoEncryption()
                    )
                )
        except Exception as e:
            raise ValueError(f"无法解析ed25519私钥: {e}")

    def _sign_request(self, query_string: str) -> str:
        """使用ed25519签名请求"""
        if ED25519_LIB == 'ed25519':
            signature = self.signing_key.sign(query_string.encode('utf-8'))
            return base64.b64encode(signature).decode('utf-8')
        else:
            # 使用cryptography库
            signature = self.signing_key.sign(query_string.encode('utf-8'))
            return base64.b64encode(signature).decode('utf-8')

    def _make_request(self, method: str, endpoint: str, params: Dict = None, signed: bool = False) -> Dict:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        if params is None:
            params = {}

        if signed:
            # 添加时间戳
            params['timestamp'] = int(time.time() * 1000)

            # 创建查询字符串用于签名（不进行URL编码）
            query_string = '&'.join([f"{k}={v}" for k, v in sorted(params.items())])

            # 签名
            signature = self._sign_request(query_string)
            params['signature'] = signature

        try:
            if method.upper() == 'GET':
                response = requests.get(url, params=params, headers=headers, timeout=30)
            elif method.upper() == 'POST':
                # POST请求使用data参数，不是json
                response = requests.post(url, data=params, headers=headers, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            # 提供更详细的错误信息
            error_msg = f"API请求失败: {e}"
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_detail = e.response.json()
                    error_msg += f" - 详细信息: {error_detail}"
                except:
                    error_msg += f" - 响应内容: {e.response.text}"
            raise Exception(error_msg)

    def get_account(self) -> Dict:
        """获取账户信息"""
        return self._make_request('GET', '/api/v3/account', signed=True)

    def get_all_tickers(self) -> List[Dict]:
        """获取所有交易对价格"""
        return self._make_request('GET', '/api/v3/ticker/price')

    def get_symbol_info(self, symbol: str) -> Dict:
        """获取交易对信息"""
        exchange_info = self._make_request('GET', '/api/v3/exchangeInfo')
        for symbol_info in exchange_info['symbols']:
            if symbol_info['symbol'] == symbol:
                return symbol_info
        raise ValueError(f"找不到交易对: {symbol}")

    def order_market_buy(self, symbol: str, quantity: str = None, quoteOrderQty: str = None) -> Dict:
        """市价买单
        Args:
            symbol: 交易对符号
            quantity: 买入数量（按基础资产）
            quoteOrderQty: 买入金额（按报价资产，如USDT）
        """
        params = {
            'symbol': symbol,
            'side': 'BUY',
            'type': 'MARKET'
        }

        if quoteOrderQty:
            # 使用USDT金额买入
            params['quoteOrderQty'] = quoteOrderQty
        elif quantity:
            # 使用数量买入
            params['quantity'] = quantity
        else:
            raise ValueError("必须提供 quantity 或 quoteOrderQty 参数")

        return self._make_request('POST', '/api/v3/order', params, signed=True)

    def order_market_sell(self, symbol: str, quantity: str) -> Dict:
        """市价卖单"""
        params = {
            'symbol': symbol,
            'side': 'SELL',
            'type': 'MARKET',
            'quantity': quantity
        }
        return self._make_request('POST', '/api/v3/order', params, signed=True)


# 为了兼容原有代码，创建一个包装类
class BinanceAPIException(Exception):
    """Binance API异常"""
    pass


def create_ed25519_client(api_key: str, private_key_pem: str, testnet: bool = False):
    """创建ed25519客户端的工厂函数"""
    try:
        return BinanceEd25519Client(api_key, private_key_pem, testnet)
    except Exception as e:
        raise BinanceAPIException(f"创建ed25519客户端失败: {e}")
