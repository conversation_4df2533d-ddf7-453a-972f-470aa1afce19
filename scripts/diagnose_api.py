#!/usr/bin/env python3
"""
诊断API问题
"""

import os
import time
import requests

# 设置代理
os.environ['http_proxy'] = "http://127.0.0.1:7890"
os.environ['https_proxy'] = "http://127.0.0.1:7890"

def check_time_sync():
    """检查时间同步"""
    print("🕐 检查时间同步...")
    
    try:
        # 获取Binance服务器时间
        response = requests.get('https://api.binance.com/api/v3/time', timeout=10)
        server_time = response.json()['serverTime']
        
        # 获取本地时间
        local_time = int(time.time() * 1000)
        
        # 计算时间差
        time_diff = abs(server_time - local_time)
        
        print(f"服务器时间: {server_time}")
        print(f"本地时间: {local_time}")
        print(f"时间差: {time_diff} ms")
        
        if time_diff > 5000:  # 5秒
            print("⚠️ 时间差过大，可能导致签名失败")
            return False
        else:
            print("✅ 时间同步正常")
            return True
            
    except Exception as e:
        print(f"❌ 时间检查失败: {e}")
        return False

def check_network():
    """检查网络连接"""
    print("\n🌐 检查网络连接...")
    
    try:
        # 测试基本连接
        response = requests.get('https://api.binance.com/api/v3/ping', timeout=10)
        if response.status_code == 200:
            print("✅ 网络连接正常")
            return True
        else:
            print(f"❌ 网络连接异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
        return False

def check_api_key_format():
    """检查API密钥格式"""
    print("\n🔑 检查API密钥格式...")
    
    api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
    
    print(f"API Key长度: {len(api_key)}")
    print(f"API Key: {api_key[:10]}...{api_key[-10:]}")
    
    # 检查字符
    valid_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789')
    api_key_chars = set(api_key)
    
    if api_key_chars.issubset(valid_chars):
        print("✅ API密钥格式正确")
        return True
    else:
        invalid_chars = api_key_chars - valid_chars
        print(f"❌ API密钥包含无效字符: {invalid_chars}")
        return False

def test_public_endpoints():
    """测试公开端点"""
    print("\n📊 测试公开端点...")
    
    try:
        # 测试价格信息
        response = requests.get('https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT', timeout=10)
        if response.status_code == 200:
            price_data = response.json()
            print(f"✅ 获取BTC价格成功: {price_data['price']}")
            return True
        else:
            print(f"❌ 获取价格失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 公开端点测试失败: {e}")
        return False

def test_signature_with_recvWindow():
    """测试带recvWindow的签名"""
    print("\n🔐 测试带recvWindow的签名...")
    
    from binance_ed25519_client import create_ed25519_client
    
    api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
    
    try:
        client = create_ed25519_client(api_key, private_key_pem, testnet=False)
        
        # 手动构建带recvWindow的请求
        import base64
        from cryptography.hazmat.primitives.serialization import load_pem_private_key
        
        private_key = load_pem_private_key(private_key_pem.encode('utf-8'), password=None)
        
        params = {
            'recvWindow': '5000',
            'timestamp': int(time.time() * 1000)
        }
        
        # 创建签名
        payload = '&'.join([f'{param}={value}' for param, value in sorted(params.items())])
        signature = private_key.sign(payload.encode('ASCII'))
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        
        print(f"载荷: {payload}")
        print(f"签名: {signature_b64}")
        
        # 发送请求
        headers = {'X-MBX-APIKEY': api_key}
        params['signature'] = signature_b64
        
        response = requests.get(
            'https://api.binance.com/api/v3/account',
            params=params,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 带recvWindow的签名成功")
            return True
        else:
            print(f"❌ 带recvWindow的签名失败: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ recvWindow测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 API诊断工具")
    print("=" * 60)
    
    results = []
    
    results.append(check_time_sync())
    results.append(check_network())
    results.append(check_api_key_format())
    results.append(test_public_endpoints())
    results.append(test_signature_with_recvWindow())
    
    print("\n" + "=" * 60)
    print("📋 诊断结果:")
    
    tests = [
        "时间同步",
        "网络连接", 
        "API密钥格式",
        "公开端点",
        "签名测试"
    ]
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {test}: {status}")
    
    if all(results):
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠️ 部分测试失败，请检查相关问题")

if __name__ == '__main__':
    main()
