#!/bin/bash

# AWS运行脚本

echo "🚀 启动AWS持仓管理脚本"
echo "================================"

# 激活conda环境
echo "🔧 激活conda环境..."
source ~/miniconda3/bin/activate

# 检查Python版本
echo "🐍 Python版本:"
python3 --version

# 安装依赖
echo "📦 安装依赖..."
pip install requests cryptography

# 检查文件
echo "📁 检查文件..."
if [ -f "balance_portfolio.py" ]; then
    echo "✅ balance_portfolio.py 存在"
else
    echo "❌ balance_portfolio.py 不存在"
    exit 1
fi

if [ -f "binance_ed25519_client.py" ]; then
    echo "✅ binance_ed25519_client.py 存在"
else
    echo "❌ binance_ed25519_client.py 不存在"
    exit 1
fi

# 测试网络连接
echo "🌐 测试网络连接..."
curl -s https://api.binance.com/api/v3/ping > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接失败"
    exit 1
fi

# 运行测试脚本
echo "🧪 运行环境测试..."
python3 test_aws.py

echo ""
echo "================================"
echo "🎯 准备运行持仓管理脚本"
echo "使用以下命令运行:"
echo ""
echo "# 模拟运行 (推荐先测试)"
echo "python3 balance_portfolio.py --symbols BTC ETH --amount 20.0"
echo ""
echo "# 实际交易 (确认无误后)"
echo "python3 balance_portfolio.py --symbols BTC ETH --amount 20.0 --live"
echo ""
echo "================================"
