#!/usr/bin/env python3
"""
离线测试持仓管理脚本
"""

import json
import logging
from decimal import Decimal
from typing import Dict, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockBinanceClient:
    """模拟Binance客户端"""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        logger.info("模拟Binance客户端初始化成功")
    
    def get_account(self):
        """模拟账户信息"""
        return {
            'balances': [
                {'asset': 'USDT', 'free': '100.0', 'locked': '0.0'},
                {'asset': 'BTC', 'free': '0.001', 'locked': '0.0'},
                {'asset': 'ETH', 'free': '0.0', 'locked': '0.0'},
                {'asset': 'BNB', 'free': '0.5', 'locked': '0.0'},
            ]
        }
    
    def get_all_tickers(self):
        """模拟价格信息"""
        return [
            {'symbol': 'BTCUSDT', 'price': '45000.0'},
            {'symbol': 'ETHUSDT', 'price': '3000.0'},
            {'symbol': 'BNBUSDT', 'price': '300.0'},
        ]
    
    def get_symbol_info(self, symbol: str):
        """模拟交易对信息"""
        return {
            'symbol': symbol,
            'filters': [
                {
                    'filterType': 'LOT_SIZE',
                    'stepSize': '0.********'
                }
            ]
        }
    
    def order_market_buy(self, symbol: str, quantity: str):
        """模拟市价买单"""
        logger.info(f"模拟买单: {symbol}, 数量: {quantity}")
        return {
            'orderId': 12345,
            'symbol': symbol,
            'side': 'BUY',
            'type': 'MARKET',
            'quantity': quantity
        }

class MockPortfolioManager:
    def __init__(self, ak: str, sk: str, target_symbols: List[str] = None, target_amount: float = 20.0, use_ed25519: bool = False):
        """初始化模拟持仓管理器"""
        self.client = MockBinanceClient(ak, sk, testnet=False)
        
        # 目标币种列表（不包含USDT）
        self.target_symbols = target_symbols or ['BTC', 'ETH', 'BNB', 'ADA', 'DOT']
        # 每个币种的目标USDT价值
        self.target_amount = Decimal(str(target_amount))
        # 最小交易金额（USDT）
        self.min_trade_amount = Decimal('1.0')
        # 价格容差（避免频繁小额交易）
        self.tolerance = Decimal('0.10')  # 10%

    def get_account_balances(self) -> Dict[str, Decimal]:
        """获取账户余额（只返回非零余额）"""
        account_info = self.client.get_account()
        balances = {}

        for balance in account_info['balances']:
            asset = balance['asset']
            free = Decimal(balance['free'])
            locked = Decimal(balance['locked'])
            total = free + locked

            if total > 0:
                balances[asset] = total
                logger.info(f"{asset}: {total}")

        return balances

    def get_prices(self, symbols: List[str]) -> Dict[str, Decimal]:
        """获取价格信息"""
        prices = {}
        ticker_prices = self.client.get_all_tickers()

        for ticker in ticker_prices:
            symbol = ticker['symbol']
            if symbol in symbols:
                prices[symbol] = Decimal(ticker['price'])

        return prices

    def calculate_usdt_values(self, balances: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """计算目标币种的USDT价值"""
        usdt_values = {}

        # 只计算目标币种的价值
        symbols_needed = []
        for asset in self.target_symbols:
            symbols_needed.append(f"{asset}USDT")

        if symbols_needed:
            prices = self.get_prices(symbols_needed)

            for asset in self.target_symbols:
                symbol = f"{asset}USDT"
                if symbol in prices:
                    balance = balances.get(asset, Decimal('0'))
                    usdt_values[asset] = balance * prices[symbol]
                    logger.info(f"{asset}: {balance} * {prices[symbol]} = {usdt_values[asset]} USDT")
                else:
                    logger.warning(f"无法获取 {symbol} 的价格")
                    usdt_values[asset] = Decimal('0')

        return usdt_values

    def calculate_target_allocation(self, usdt_values: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """计算目标分配"""
        total_current_value = sum(usdt_values.values())
        num_assets = len(self.target_symbols)
        total_target_value = self.target_amount * num_assets

        logger.info(f"当前总价值: {total_current_value} USDT")
        logger.info(f"目标币种数量: {num_assets}")
        logger.info(f"每个币种目标价值: {self.target_amount} USDT")
        logger.info(f"总目标价值: {total_target_value} USDT")

        target_allocation = {}
        for asset in self.target_symbols:
            target_allocation[asset] = self.target_amount

        return target_allocation

    def calculate_trades(self, balances: Dict[str, Decimal], usdt_values: Dict[str, Decimal],
                        target_allocation: Dict[str, Decimal]) -> List[Dict]:
        """计算需要执行的交易"""
        trades = []

        # 获取当前价格
        symbols_needed = []
        for asset in self.target_symbols:
            symbols_needed.append(f"{asset}USDT")

        prices = self.get_prices(symbols_needed) if symbols_needed else {}

        # 检查USDT余额
        usdt_balance = balances.get('USDT', Decimal('0'))
        logger.info(f"当前USDT余额: {usdt_balance}")

        for asset in self.target_symbols:
            current_value = usdt_values.get(asset, Decimal('0'))
            target_value = target_allocation[asset]
            difference = target_value - current_value

            logger.info(f"{asset}: 当前价值 {current_value} USDT, 目标价值 {target_value} USDT, 差额 {difference} USDT")

            # 检查是否需要交易（超过容差且超过最小交易金额）
            if difference > self.min_trade_amount and abs(difference) > target_value * self.tolerance:
                symbol = f"{asset}USDT"
                if symbol in prices:
                    # 需要买入
                    if difference > 0:
                        # 检查USDT余额是否足够
                        if usdt_balance >= difference:
                            trades.append({
                                'action': 'buy',
                                'symbol': symbol,
                                'usdt_amount': difference,
                                'asset': asset,
                                'price': prices[symbol]
                            })
                            usdt_balance -= difference  # 更新可用USDT余额
                        else:
                            logger.warning(f"USDT余额不足，无法买入 {asset}。需要 {difference} USDT，但只有 {usdt_balance} USDT")
                else:
                    logger.warning(f"无法获取 {symbol} 的价格，跳过交易")

        return trades

    def manage_portfolio(self, dry_run: bool = True):
        """执行投资组合管理"""
        logger.info("开始投资组合管理...")
        logger.info(f"目标币种: {self.target_symbols}")
        logger.info(f"每个币种目标价值: {self.target_amount} USDT")

        # 1. 获取当前余额
        logger.info("获取账户余额...")
        balances = self.get_account_balances()

        # 2. 计算目标币种的USDT价值
        logger.info("计算目标币种的USDT价值...")
        usdt_values = self.calculate_usdt_values(balances)

        # 3. 计算目标分配
        logger.info("计算目标分配...")
        target_allocation = self.calculate_target_allocation(usdt_values)

        # 4. 计算需要的交易
        logger.info("计算需要的交易...")
        trades = self.calculate_trades(balances, usdt_values, target_allocation)

        if not trades:
            logger.info("所有目标币种持仓已达到目标，无需交易")
            return

        # 5. 显示交易计划
        logger.info("交易计划:")
        for i, trade in enumerate(trades, 1):
            logger.info(f"{i}. {trade}")

        # 6. 执行交易
        if dry_run:
            logger.info("这是模拟运行，不会执行实际交易")
        else:
            logger.info("开始执行模拟交易...")
            for i, trade in enumerate(trades, 1):
                logger.info(f"执行交易 {i}/{len(trades)}")
                # 模拟交易执行
                if trade['action'] == 'buy':
                    quantity = trade['usdt_amount'] / trade['price']
                    self.client.order_market_buy(trade['symbol'], str(quantity))
                    logger.info(f"交易 {i} 成功")

        logger.info("投资组合管理完成")

def main():
    """测试主函数"""
    logger.info("开始离线测试...")
    
    # 创建模拟管理器
    manager = MockPortfolioManager(
        ak="test_api_key",
        sk="test_secret",
        target_symbols=['BTC', 'ETH'],
        target_amount=20.0,
        use_ed25519=True
    )
    
    # 执行模拟管理
    manager.manage_portfolio(dry_run=True)
    
    logger.info("离线测试完成！")

if __name__ == '__main__':
    main()
