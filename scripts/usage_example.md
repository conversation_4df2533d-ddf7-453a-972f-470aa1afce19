# 持仓管理脚本使用说明

## 安装依赖

```bash
# 安装基础依赖
pip install requests

# 选择一种ed25519库（推荐cryptography）
pip install cryptography
# 或者
pip install ed25519

# 如果要使用传统HMAC签名，安装python-binance
pip install python-binance
```

## 使用方法

### 1. 使用传统HMAC签名（推荐）

如果你有传统的Binance API Secret（32字符十六进制字符串）：

```bash
python3 balance_portfolio.py \
    --api_key "你的API_KEY" \
    --api_secret "你的API_SECRET" \
    --symbols BTC ETH BNB \
    --amount 20.0 \
    --live
```

### 2. 使用ed25519签名

如果你有ed25519私钥：

```bash
python3 balance_portfolio.py \
    --api_key "你的API_KEY" \
    --use_ed25519 \
    --ed25519_key "-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----" \
    --symbols BTC ETH BNB \
    --amount 20.0 \
    --live
```

### 3. 模拟运行（测试）

```bash
# 不加--live参数就是模拟运行
python3 balance_portfolio.py \
    --api_key "你的API_KEY" \
    --api_secret "你的API_SECRET" \
    --symbols BTC ETH \
    --amount 50.0
```

## 参数说明

- `--api_key`: Binance API Key
- `--api_secret`: 传统API Secret（HMAC签名用）
- `--ed25519_key`: ed25519私钥PEM格式
- `--use_ed25519`: 使用ed25519签名
- `--symbols`: 目标币种列表
- `--amount`: 每个币种目标金额（USDT）
- `--live`: 执行实际交易（不加此参数为模拟运行）

## 注意事项

1. **签名方式选择**：
   - 大多数用户应该使用传统HMAC签名
   - 只有在确定需要ed25519时才使用

2. **安全提醒**：
   - 不要在命令行中直接输入真实的API密钥
   - 建议使用环境变量或配置文件

3. **测试建议**：
   - 首次使用时先进行模拟运行
   - 确认交易计划正确后再使用--live参数

## 故障排除

### 签名错误
- 确认API Key和Secret正确
- 检查时间同步
- 确认选择了正确的签名方式

### 依赖问题
```bash
# 如果遇到导入错误，安装相应依赖
pip install cryptography requests
```
