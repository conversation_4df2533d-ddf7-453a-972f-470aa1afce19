#!/usr/bin/env python3
"""
测试API调用格式
"""

import logging
from binance_ed25519_client import create_ed25519_client

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_api_format():
    """测试API调用格式"""
    
    api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""

    try:
        logger.info("创建ed25519客户端...")
        client = create_ed25519_client(api_key, private_key_pem, testnet=False)
        
        logger.info("测试获取账户信息...")
        try:
            account = client.get_account()
            logger.info("✓ 账户信息获取成功")
            
            # 显示余额
            for balance in account['balances']:
                if float(balance['free']) > 0 or float(balance['locked']) > 0:
                    logger.info(f"  {balance['asset']}: {balance['free']} (可用) + {balance['locked']} (锁定)")
                    
        except Exception as e:
            logger.error(f"✗ 账户信息获取失败: {e}")
        
        logger.info("测试获取价格信息...")
        try:
            tickers = client.get_all_tickers()
            logger.info(f"✓ 价格信息获取成功，共 {len(tickers)} 个交易对")
            
            # 显示几个主要币种价格
            for ticker in tickers[:5]:
                if ticker['symbol'] in ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']:
                    logger.info(f"  {ticker['symbol']}: {ticker['price']}")
                    
        except Exception as e:
            logger.error(f"✗ 价格信息获取失败: {e}")
        
        logger.info("测试交易对信息...")
        try:
            symbol_info = client.get_symbol_info('BTCUSDT')
            logger.info("✓ 交易对信息获取成功")
            logger.info(f"  交易对: {symbol_info['symbol']}")
            logger.info(f"  状态: {symbol_info.get('status', 'N/A')}")
            
        except Exception as e:
            logger.error(f"✗ 交易对信息获取失败: {e}")
        
        # 注意：不要在测试中执行实际交易
        logger.info("⚠ 跳过实际交易测试以避免意外损失")
        
        logger.info("API格式测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_format()
