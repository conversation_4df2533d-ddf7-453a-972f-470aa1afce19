#!/usr/bin/env python3
"""
AWS环境调试脚本
"""

import sys
import time
import subprocess

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查conda环境
    try:
        result = subprocess.run(['conda', 'info'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Conda环境正常")
        else:
            print("⚠️ Conda环境异常")
    except:
        print("❌ 未找到Conda")

def check_dependencies():
    """检查依赖"""
    print("\n📦 检查依赖...")
    
    deps = ['requests', 'cryptography']
    
    for dep in deps:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} - 需要安装")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', dep], check=True)
                print(f"✅ {dep} - 安装成功")
            except:
                print(f"❌ {dep} - 安装失败")

def test_api_connection():
    """测试API连接"""
    print("\n🌐 测试API连接...")
    
    try:
        import requests
        
        # 测试ping
        response = requests.get('https://api.binance.com/api/v3/ping', timeout=10)
        if response.status_code == 200:
            print("✅ Binance API ping成功")
        else:
            print(f"❌ Ping失败: {response.status_code}")
            return False
        
        # 测试时间同步
        time_response = requests.get('https://api.binance.com/api/v3/time', timeout=10)
        server_time = time_response.json()['serverTime']
        local_time = int(time.time() * 1000)
        time_diff = abs(server_time - local_time)
        
        print(f"服务器时间: {server_time}")
        print(f"本地时间: {local_time}")
        print(f"时间差: {time_diff}ms")
        
        if time_diff > 5000:
            print("⚠️ 时间差过大")
            return False
        else:
            print("✅ 时间同步正常")
            return True
            
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def test_ed25519():
    """测试ed25519"""
    print("\n🔐 测试ed25519...")
    
    try:
        from cryptography.hazmat.primitives.serialization import load_pem_private_key
        import base64
        
        private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
        
        # 加载私钥
        private_key = load_pem_private_key(private_key_pem.encode('utf-8'), password=None)
        print("✅ 私钥加载成功")
        
        # 测试签名
        test_message = "test_message"
        signature = private_key.sign(test_message.encode('ASCII'))
        signature_b64 = base64.b64encode(signature).decode('utf-8')
        print(f"✅ 签名成功: {signature_b64[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ ed25519测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_client():
    """测试客户端"""
    print("\n🔧 测试客户端...")
    
    try:
        from binance_ed25519_client import create_ed25519_client
        
        api_key = "iZzgi0SopW2OQPJ6G5n3Uh5i16qVlXmD2DRWtWdZe7yr0h40XeZGgPUyGJJTDa50"
        private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEICdGYzmk06pyU6ugkMosSBYCyC/qEzoomFe8A7fwMzkT
-----END PRIVATE KEY-----"""
        
        client = create_ed25519_client(api_key, private_key_pem, testnet=False)
        print("✅ 客户端创建成功")
        
        # 测试公开API
        try:
            tickers = client.get_all_tickers()
            print(f"✅ 获取价格成功: {len(tickers)}个交易对")
        except Exception as e:
            print(f"⚠️ 获取价格失败: {e}")
        
        # 测试私有API
        try:
            account = client.get_account()
            print("✅ 获取账户信息成功")
            
            # 显示余额
            usdt_balance = 0
            for balance in account['balances']:
                if balance['asset'] == 'USDT':
                    usdt_balance = float(balance['free'])
                    break
            
            print(f"💰 USDT余额: {usdt_balance}")
            return True
            
        except Exception as e:
            print(f"❌ 获取账户信息失败: {e}")
            if "Invalid API-key" in str(e):
                print("🔑 API密钥问题")
            elif "Signature" in str(e):
                print("🔐 签名问题")
            return False
            
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 AWS环境调试")
    print("=" * 40)
    
    check_environment()
    check_dependencies()
    
    api_ok = test_api_connection()
    ed25519_ok = test_ed25519()
    client_ok = test_client()
    
    print("\n" + "=" * 40)
    print("📋 调试结果:")
    print(f"API连接: {'✅' if api_ok else '❌'}")
    print(f"ed25519: {'✅' if ed25519_ok else '❌'}")
    print(f"客户端: {'✅' if client_ok else '❌'}")
    
    if api_ok and ed25519_ok and client_ok:
        print("\n🎉 环境正常，可以运行持仓管理脚本")
        print("\n运行命令:")
        print("python3 balance_portfolio.py --symbols BTC ETH --amount 20.0")
    else:
        print("\n⚠️ 环境有问题，请检查上述错误")

if __name__ == '__main__':
    main()
